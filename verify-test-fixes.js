#!/usr/bin/env node

/**
 * Test Fix Verification Script
 * Verifies that test files have been properly fixed
 */

const fs = require('fs');
const path = require('path');

function checkFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return { status: 'missing', issues: [`File ${filePath} does not exist`] };
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const issues = [];

  // Check for await import issues
  if (content.includes('await import') && !filePath.includes('integration/component-integration')) {
    issues.push('Contains problematic "await import" pattern');
  }

  // Check for proper mock setup
  if (content.includes('vi.mock') && content.includes('query')) {
    if (!content.includes('const mockQuery = vi.fn()')) {
      issues.push('Missing proper mockQuery setup');
    }
  }

  // Check for fetch mocking
  if (content.includes('mockFetch') && !content.includes('const mockFetch = vi.fn()')) {
    issues.push('Missing proper mockFetch setup');
  }

  return {
    status: issues.length === 0 ? 'ok' : 'issues',
    issues
  };
}

function verifyTestFiles() {
  console.log('🔍 Verifying Test File Fixes');
  console.log('============================');

  const testFiles = [
    'src/__tests__/analytics-simple.test.ts',
    'src/__tests__/user-authentication.test.ts',
    'src/__tests__/api-integration.test.ts',
    'src/__tests__/admin-benefit-management.test.ts',
    'src/__tests__/admin-user-management.test.ts',
    'src/__tests__/user-company-interactions.test.ts',
    'src/__tests__/user-analytics-insights.test.ts',
    'src/__tests__/admin-company-management.test.ts',
    'src/__tests__/user-benefit-management.test.ts',
    'src/__tests__/admin-analytics-management.test.ts',
    'src/__tests__/analytics.test.ts'
  ];

  let totalIssues = 0;
  let fixedFiles = 0;

  testFiles.forEach(file => {
    const result = checkFile(file);
    
    if (result.status === 'missing') {
      console.log(`❌ ${file}: MISSING`);
      totalIssues += result.issues.length;
    } else if (result.status === 'issues') {
      console.log(`⚠️  ${file}: ${result.issues.length} issues`);
      result.issues.forEach(issue => console.log(`   - ${issue}`));
      totalIssues += result.issues.length;
    } else {
      console.log(`✅ ${file}: OK`);
      fixedFiles++;
    }
  });

  console.log('\n📊 Summary');
  console.log('===========');
  console.log(`Files checked: ${testFiles.length}`);
  console.log(`Files fixed: ${fixedFiles}`);
  console.log(`Total issues: ${totalIssues}`);

  // Check setup files
  console.log('\n🔧 Setup Files');
  console.log('===============');
  
  const setupResult = checkFile('vitest.setup.ts');
  if (setupResult.status === 'ok') {
    console.log('✅ vitest.setup.ts: OK');
  } else {
    console.log(`❌ vitest.setup.ts: ${setupResult.issues.join(', ')}`);
    totalIssues += setupResult.issues.length;
  }

  // Check for removed backup files
  const backupFile = 'src/__tests__/location-components.test.tsx.bak';
  if (fs.existsSync(backupFile)) {
    console.log(`❌ Backup file still exists: ${backupFile}`);
    totalIssues++;
  } else {
    console.log('✅ Backup files removed');
  }

  console.log('\n🎯 Overall Status');
  console.log('=================');
  if (totalIssues === 0) {
    console.log('🎉 All test files have been successfully fixed!');
    console.log('✅ Ready to run tests');
    return true;
  } else {
    console.log(`❌ ${totalIssues} issues remaining`);
    console.log('🔧 Additional fixes needed');
    return false;
  }
}

// Run verification
const success = verifyTestFiles();
process.exit(success ? 0 : 1);
