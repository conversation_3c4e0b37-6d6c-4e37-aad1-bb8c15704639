import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { query } from '@/lib/local-db'

/**
 * GET /api/admin/stats - Get admin dashboard statistics
 * Returns total counts for companies, users, and benefits
 */
export async function GET(_request: NextRequest) {
  try {
    await requireAdmin()
    
    // Get total counts for companies, users, and benefits
    const [
      companiesResult,
      usersResult,
      benefitsResult
    ] = await Promise.all([
      query('SELECT COUNT(*) as count FROM companies'),
      query('SELECT COUNT(*) as count FROM users'),
      query('SELECT COUNT(*) as count FROM benefits')
    ])
    
    const stats = {
      totalCompanies: parseInt(companiesResult.rows[0]?.count || 0),
      totalUsers: parseInt(usersResult.rows[0]?.count || 0),
      totalBenefits: parseInt(benefitsResult.rows[0]?.count || 0)
    }
    
    return NextResponse.json(stats)
    
  } catch (error) {
    console.error('Error fetching admin stats:', error)
    return NextResponse.json(
      { error: 'Failed to fetch admin statistics' },
      { status: 500 }
    )
  }
}
