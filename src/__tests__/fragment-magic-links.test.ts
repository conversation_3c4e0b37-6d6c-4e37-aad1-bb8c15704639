/**
 * Fragment-based Magic Link Tests
 * Tests that magic links now use URL fragments instead of query parameters
 * to prevent email scanners from pre-opening them
 */

import { describe, it, expect } from 'vitest'
import { createSignInMagicLinkEmail, createSignUpMagicLinkEmail } from '@/lib/magic-link-auth'

describe('Fragment-based Magic Links', () => {
  it('should create sign-in magic links with fragments instead of query parameters', () => {
    const email = '<EMAIL>'
    const token = 'test-token-123'
    
    const emailOptions = createSignInMagicLinkEmail(email, token)
    
    // Check that the HTML contains fragment-based URL
    expect(emailOptions.html).toContain('/auth/magic-link#test-token-123')
    expect(emailOptions.html).not.toContain('/auth/magic-link?token=')
    
    // Check that the text version also uses fragment
    expect(emailOptions.text).toContain('/auth/magic-link#test-token-123')
    expect(emailOptions.text).not.toContain('/auth/magic-link?token=')
  })

  it('should create sign-up magic links with fragments instead of query parameters', () => {
    const email = '<EMAIL>'
    const firstName = 'New'
    const token = 'signup-token-456'
    
    const emailOptions = createSignUpMagicLinkEmail(email, firstName, token)
    
    // Check that the HTML contains fragment-based URL
    expect(emailOptions.html).toContain('/auth/magic-link#signup-token-456')
    expect(emailOptions.html).not.toContain('/auth/magic-link?token=')
    
    // Check that the text version also uses fragment
    expect(emailOptions.text).toContain('/auth/magic-link#signup-token-456')
    expect(emailOptions.text).not.toContain('/auth/magic-link?token=')
  })

  it('should use the correct base URL from environment', () => {
    const originalAppUrl = process.env.APP_URL
    
    // Test with custom APP_URL
    process.env.APP_URL = 'https://benefitlens.com'
    
    const email = '<EMAIL>'
    const token = 'env-test-token'
    
    const emailOptions = createSignInMagicLinkEmail(email, token)
    
    expect(emailOptions.html).toContain('https://benefitlens.com/auth/magic-link#env-test-token')
    expect(emailOptions.text).toContain('https://benefitlens.com/auth/magic-link#env-test-token')
    
    // Restore original APP_URL
    if (originalAppUrl) {
      process.env.APP_URL = originalAppUrl
    } else {
      delete process.env.APP_URL
    }
  })

  it('should fall back to localhost when APP_URL is not set', () => {
    const originalAppUrl = process.env.APP_URL
    delete process.env.APP_URL
    
    const email = '<EMAIL>'
    const token = 'localhost-test-token'
    
    const emailOptions = createSignInMagicLinkEmail(email, token)
    
    expect(emailOptions.html).toContain('http://localhost:3000/auth/magic-link#localhost-test-token')
    expect(emailOptions.text).toContain('http://localhost:3000/auth/magic-link#localhost-test-token')
    
    // Restore original APP_URL
    if (originalAppUrl) {
      process.env.APP_URL = originalAppUrl
    }
  })
})
