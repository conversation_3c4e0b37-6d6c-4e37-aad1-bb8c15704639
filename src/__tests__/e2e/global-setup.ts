/**
 * Global setup for E2E tests
 * Prepares test database and environment
 */

import { FullConfig } from '@playwright/test'
import { config } from 'dotenv'
import { query } from '@/lib/local-db'

// Load environment variables
config({ path: '.env.local' })

async function globalSetup(config: FullConfig) {
  console.log('🎭 Starting E2E test setup...')

  try {
    // Disable rate limiting for E2E tests to prevent timeouts
    process.env.DISABLE_RATE_LIMITING = 'true'
    console.log('🎭 Rate limiting disabled for E2E tests')

    // Set up database for E2E tests
    await setupE2EDatabase()

    // Wait for server to be ready
    await waitForServer(config.webServer?.url || 'http://localhost:3000')

    console.log('✅ E2E test setup complete')
  } catch (error) {
    console.error('❌ E2E test setup failed:', error)
    throw error // Fail fast if setup fails
  }
}

async function setupE2EDatabase() {
  try {
    // Clean up any existing E2E test data
    await cleanupE2EDatabase()
    
    // Create E2E test data
    await seedE2EDatabase()
    
    console.log('🎭 E2E test database setup complete')
  } catch (error) {
    console.error('❌ E2E test database setup failed:', error)
    throw error
  }
}

async function cleanupE2EDatabase() {
  console.log('🎭 Cleaning up E2E test database...')

  const cleanupQueries = [
    // Clear rate limiting data for e2e test emails
    'DELETE FROM magic_link_rate_limits WHERE email LIKE \'%@e2e.test\' OR email LIKE \'%@%.e2e\'',
    'DELETE FROM rate_limits WHERE identifier LIKE \'%@e2e.test\' OR identifier LIKE \'%@%.e2e\'',
    // Clear test data
    'DELETE FROM magic_link_tokens WHERE email LIKE \'%@e2e.test\' OR email LIKE \'%@%.e2e\'',
    'DELETE FROM user_benefit_rankings WHERE user_id IN (SELECT id FROM users WHERE email LIKE \'%@e2e.test\' OR email LIKE \'%@%.e2e\')',
    'DELETE FROM company_benefits WHERE company_id IN (SELECT id FROM companies WHERE domain LIKE \'%.e2e\')',
    'DELETE FROM company_locations WHERE company_id IN (SELECT id FROM companies WHERE domain LIKE \'%.e2e\')',
    'DELETE FROM user_sessions WHERE user_id IN (SELECT id FROM users WHERE email LIKE \'%@e2e.test\' OR email LIKE \'%@%.e2e\')',
    'DELETE FROM users WHERE email LIKE \'%@e2e.test\' OR email LIKE \'%@%.e2e\'',
    'DELETE FROM companies WHERE domain LIKE \'%.e2e\'',
    'DELETE FROM benefits WHERE name LIKE \'E2E %\'',
    'DELETE FROM benefit_categories WHERE name LIKE \'e2e_%\'',
  ]

  for (const sql of cleanupQueries) {
    try {
      await query(sql)
    } catch (error) {
      console.warn('Cleanup query failed (may be expected):', sql, error)
    }
  }
}

async function seedE2EDatabase() {
  console.log('🎭 Seeding E2E test database...')

  // First clean up any existing e2e data
  await cleanupE2EDatabase()

  // Create E2E test benefit categories (let PostgreSQL generate UUIDs)
  await query(`
    INSERT INTO benefit_categories (name, display_name, icon)
    SELECT 'e2e_health', 'E2E Health', '🏥'
    WHERE NOT EXISTS (SELECT 1 FROM benefit_categories WHERE name = 'e2e_health')
    UNION ALL
    SELECT 'e2e_flexibility', 'E2E Flexibility', '⏰'
    WHERE NOT EXISTS (SELECT 1 FROM benefit_categories WHERE name = 'e2e_flexibility')
    UNION ALL
    SELECT 'e2e_financial', 'E2E Financial', '💰'
    WHERE NOT EXISTS (SELECT 1 FROM benefit_categories WHERE name = 'e2e_financial')
  `)

  // Create E2E test benefits (let PostgreSQL generate UUIDs)
  await query(`
    INSERT INTO benefits (name, description, category_id, icon)
    SELECT 'E2E Health Insurance', 'Comprehensive health coverage for E2E testing',
           (SELECT id FROM benefit_categories WHERE name = 'e2e_health'), '🏥'
    WHERE NOT EXISTS (SELECT 1 FROM benefits WHERE name = 'E2E Health Insurance')
    UNION ALL
    SELECT 'E2E Remote Work', 'Flexible remote work policy for E2E testing',
           (SELECT id FROM benefit_categories WHERE name = 'e2e_flexibility'), '🏠'
    WHERE NOT EXISTS (SELECT 1 FROM benefits WHERE name = 'E2E Remote Work')
    UNION ALL
    SELECT 'E2E Dental Coverage', 'Dental insurance for E2E testing',
           (SELECT id FROM benefit_categories WHERE name = 'e2e_health'), '🦷'
    WHERE NOT EXISTS (SELECT 1 FROM benefits WHERE name = 'E2E Dental Coverage')
    UNION ALL
    SELECT 'E2E Stock Options', 'Employee stock options for E2E testing',
           (SELECT id FROM benefit_categories WHERE name = 'e2e_financial'), '📈'
    WHERE NOT EXISTS (SELECT 1 FROM benefits WHERE name = 'E2E Stock Options')
    UNION ALL
    SELECT 'E2E Gym Membership', 'Fitness center membership for E2E testing',
           (SELECT id FROM benefit_categories WHERE name = 'e2e_health'), '💪'
    WHERE NOT EXISTS (SELECT 1 FROM benefits WHERE name = 'E2E Gym Membership')
  `)

  // Create E2E test companies (let PostgreSQL generate UUIDs)
  await query(`
    INSERT INTO companies (name, domain, industry, size, description)
    SELECT 'E2E Tech Corp', 'techcorp.e2e', 'Technology', 'medium', 'Leading technology company for E2E testing'
    WHERE NOT EXISTS (SELECT 1 FROM companies WHERE domain = 'techcorp.e2e')
    UNION ALL
    SELECT 'E2E Industries', 'industries.e2e', 'Manufacturing', 'large', 'Manufacturing company for E2E testing'
    WHERE NOT EXISTS (SELECT 1 FROM companies WHERE domain = 'industries.e2e')
    UNION ALL
    SELECT 'E2E Startup', 'startup.e2e', 'Technology', 'startup', 'Innovative startup for E2E testing'
    WHERE NOT EXISTS (SELECT 1 FROM companies WHERE domain = 'startup.e2e')
    UNION ALL
    SELECT 'E2E Consulting', 'consulting.e2e', 'Consulting', 'small', 'Consulting firm for E2E testing'
    WHERE NOT EXISTS (SELECT 1 FROM companies WHERE domain = 'consulting.e2e')
  `)

  // Create E2E test admin users first (no company association needed)
  console.log('🎭 Creating admin users...')
  const adminResult = await query(`
    INSERT INTO users (email, first_name, last_name, role, payment_status, company_id, email_verified)
    VALUES
      ('<EMAIL>', 'Admin', 'User', 'admin', 'paying', null, true),
      ('<EMAIL>', 'Super', 'Admin', 'admin', 'paying', null, true),
      ('<EMAIL>', 'Admin2', 'User', 'admin', 'paying', null, true),
      ('<EMAIL>', 'Admin3', 'User', 'admin', 'paying', null, true),
      ('<EMAIL>', 'Admin4', 'User', 'admin', 'paying', null, true),
      ('<EMAIL>', 'Admin5', 'User', 'admin', 'paying', null, true),
      ('<EMAIL>', 'Admin6', 'User', 'admin', 'paying', null, true)
    ON CONFLICT (email) DO NOTHING
    RETURNING email, role
  `)
  console.log('🎭 Admin users created:', adminResult.rows)

  // Create E2E test regular users (with company associations)
  await query(`
    INSERT INTO users (email, first_name, last_name, role, payment_status, company_id, email_verified)
    SELECT 'user1@techcorp.e2e', 'John', 'Doe', 'user', 'free', c.id, true
    FROM companies c WHERE c.domain = 'techcorp.e2e'
    AND NOT EXISTS (SELECT 1 FROM users WHERE email = 'user1@techcorp.e2e')
    UNION ALL
    SELECT 'user2@industries.e2e', 'Jane', 'Smith', 'user', 'paying', c.id, true
    FROM companies c WHERE c.domain = 'industries.e2e'
    AND NOT EXISTS (SELECT 1 FROM users WHERE email = 'user2@industries.e2e')
    UNION ALL
    SELECT 'user3@startup.e2e', 'Bob', 'Johnson', 'user', 'free', c.id, true
    FROM companies c WHERE c.domain = 'startup.e2e'
    AND NOT EXISTS (SELECT 1 FROM users WHERE email = 'user3@startup.e2e')
  `)

  // Create E2E test company benefits (let PostgreSQL generate UUIDs)
  await query(`
    INSERT INTO company_benefits (company_id, benefit_id, is_verified, added_by)
    VALUES
      ((SELECT id FROM companies WHERE domain = 'techcorp.e2e'),
       (SELECT id FROM benefits WHERE name = 'E2E Health Insurance'), true, 'admin'),
      ((SELECT id FROM companies WHERE domain = 'techcorp.e2e'),
       (SELECT id FROM benefits WHERE name = 'E2E Remote Work'), true, 'admin'),
      ((SELECT id FROM companies WHERE domain = 'techcorp.e2e'),
       (SELECT id FROM benefits WHERE name = 'E2E Stock Options'), true, 'admin'),
      ((SELECT id FROM companies WHERE domain = 'industries.e2e'),
       (SELECT id FROM benefits WHERE name = 'E2E Health Insurance'), true, 'admin'),
      ((SELECT id FROM companies WHERE domain = 'industries.e2e'),
       (SELECT id FROM benefits WHERE name = 'E2E Dental Coverage'), false, 'user'),
      ((SELECT id FROM companies WHERE domain = 'startup.e2e'),
       (SELECT id FROM benefits WHERE name = 'E2E Remote Work'), false, 'user')
    ON CONFLICT (company_id, benefit_id) DO NOTHING
  `)

  // Create E2E test company locations (let PostgreSQL generate UUIDs)
  await query(`
    INSERT INTO company_locations (company_id, location_raw, location_normalized, city, country, country_code, is_headquarters, is_primary)
    VALUES
      ((SELECT id FROM companies WHERE domain = 'techcorp.e2e'), 'Berlin, Germany', 'berlin-germany', 'Berlin', 'Germany', 'DE', true, true),
      ((SELECT id FROM companies WHERE domain = 'techcorp.e2e'), 'Munich, Germany', 'munich-germany', 'Munich', 'Germany', 'DE', false, false),
      ((SELECT id FROM companies WHERE domain = 'industries.e2e'), 'Hamburg, Germany', 'hamburg-germany', 'Hamburg', 'Germany', 'DE', true, true),
      ((SELECT id FROM companies WHERE domain = 'startup.e2e'), 'Frankfurt, Germany', 'frankfurt-germany', 'Frankfurt', 'Germany', 'DE', true, true)
    ON CONFLICT (company_id, location_normalized) DO NOTHING
  `)

  // Create E2E test user benefit rankings (let PostgreSQL generate UUIDs)
  await query(`
    INSERT INTO user_benefit_rankings (user_id, benefit_id, ranking)
    VALUES
      ((SELECT id FROM users WHERE email = 'user1@techcorp.e2e'),
       (SELECT id FROM benefits WHERE name = 'E2E Health Insurance'), 1),
      ((SELECT id FROM users WHERE email = 'user1@techcorp.e2e'),
       (SELECT id FROM benefits WHERE name = 'E2E Remote Work'), 2),
      ((SELECT id FROM users WHERE email = 'user2@industries.e2e'),
       (SELECT id FROM benefits WHERE name = 'E2E Health Insurance'), 1),
      ((SELECT id FROM users WHERE email = 'user2@industries.e2e'),
       (SELECT id FROM benefits WHERE name = 'E2E Dental Coverage'), 2)
    ON CONFLICT (user_id, benefit_id) DO NOTHING
  `)

  // Create E2E test magic link tokens for authentication
  console.log('🎭 Creating magic link tokens...')
  const futureExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now
  const tokenResult = await query(`
    INSERT INTO magic_link_tokens (token, email, expires_at, used_at)
    VALUES
      ('mock-token', 'user1@techcorp.e2e', $1, null),
      ('mock-token-2', 'user2@industries.e2e', $1, null),
      ('mock-token-3', 'user3@startup.e2e', $1, null),
      ('mock-admin-token', '<EMAIL>', $1, null),
      ('mock-super-admin-token', '<EMAIL>', $1, null),
      ('mock-admin2-token', '<EMAIL>', $1, null),
      ('mock-admin3-token', '<EMAIL>', $1, null),
      ('mock-admin4-token', '<EMAIL>', $1, null),
      ('mock-admin5-token', '<EMAIL>', $1, null),
      ('mock-admin6-token', '<EMAIL>', $1, null)
    ON CONFLICT (token) DO NOTHING
    RETURNING token, email
  `, [futureExpiry])
  console.log('🎭 Magic link tokens created:', tokenResult.rows)

  console.log('🎭 E2E test database seeded')
}

async function waitForServer(url: string, maxAttempts = 60) {
  console.log(`🎭 Waiting for server at ${url}...`)
  
  for (let i = 0; i < maxAttempts; i++) {
    try {
      const response = await fetch(`${url}/api/health`)
      if (response.ok) {
        console.log('✅ Server is ready')
        return true
      }
    } catch (error) {
      // Server not ready yet
    }
    
    await new Promise(resolve => setTimeout(resolve, 2000))
  }
  
  throw new Error(`Server not ready after ${maxAttempts * 2} seconds`)
}

export default globalSetup
