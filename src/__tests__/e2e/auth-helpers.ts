/**
 * E2E Test Authentication Helpers
 * Provides proper authentication flow for e2e tests
 */

import { Page } from '@playwright/test'
import { Pool } from 'pg'

// Database connection for creating unique tokens
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
})

/**
 * Create a unique magic link token for testing
 */
async function createUniqueToken(email: string): Promise<string> {
  // Create a highly unique token with timestamp and random components
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2)
  const emailHash = email.replace(/[^a-zA-Z0-9]/g, '')
  const uniqueToken = `test-token-${timestamp}-${random}-${emailHash}`
  const futureExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now

  // First, clean up any existing tokens for this email to prevent conflicts
  await pool.query(`
    DELETE FROM magic_link_tokens
    WHERE email = $1
  `, [email])

  // Add a small delay to ensure cleanup is complete
  await new Promise(resolve => setTimeout(resolve, 100))

  // Insert the new token
  await pool.query(`
    INSERT INTO magic_link_tokens (token, email, expires_at, used_at)
    VALUES ($1, $2, $3, null)
    ON CONFLICT (token) DO UPDATE SET
      expires_at = EXCLUDED.expires_at,
      used_at = null
  `, [uniqueToken, email, futureExpiry])

  return uniqueToken
}

/**
 * Sign in a user using the proper API flow
 */
export async function signInUser(page: Page, email: string = 'user1@techcorp.e2e', token?: string) {
  // Create a unique token if none provided
  if (!token) {
    token = await createUniqueToken(email)
  }

  // Add aggressive delay to prevent rate limiting
  await page.waitForTimeout(10000)

  // Navigate to sign-in page
  await page.goto('/sign-in')

  // Fill in email and submit
  await page.fill('input[type="email"]', email)
  await page.click('button[type="submit"]')

  // Wait for the magic link sent message
  try {
    await page.waitForSelector('text=Magic link sent! Please check your email', { timeout: 10000 })
  } catch (error) {
    // Check if there's a rate limit error (only if rate limiting is enabled)
    const rateLimitingDisabled = process.env.DISABLE_RATE_LIMITING === 'true'
    const errorElement = await page.locator('.bg-red-50').first()
    if (await errorElement.isVisible()) {
      const errorText = await errorElement.textContent()
      if (errorText?.includes('Too many requests') && !rateLimitingDisabled) {
        // Wait longer and retry
        console.log('Rate limit detected, waiting 20 seconds...')
        await page.waitForTimeout(20000)
        await page.click('button[type="submit"]')
        await page.waitForSelector('text=Magic link sent! Please check your email', { timeout: 15000 })
      } else {
        throw new Error(`Sign-in failed with error: ${errorText}`)
      }
    } else {
      throw error
    }
  }

  // Now simulate clicking the magic link by navigating to the magic link page
  await page.goto(`/auth/magic-link#${token}`)

  // Wait for verification to complete and redirect to dashboard
  try {
    await page.waitForURL(/\/(dashboard|$)/, { timeout: 20000 })
  } catch (error) {
    // If magic link fails, check for error messages and retry with new token
    console.log('Magic link verification failed, checking for errors...')
    await page.waitForTimeout(2000) // Wait for any error messages to appear

    const errorElement = await page.locator('.bg-red-50, .text-red-600, .error').first()
    if (await errorElement.isVisible()) {
      const errorText = await errorElement.textContent()
      console.log('Error text found:', errorText)

      if (errorText?.includes('Invalid or expired') || errorText?.includes('Magic link')) {
        console.log('Magic link expired, creating new token and retrying...')
        const newToken = await createUniqueToken(email)
        await page.waitForTimeout(1000) // Wait for token creation
        await page.goto(`/auth/magic-link#${newToken}`)
        await page.waitForURL(/\/(dashboard|$)/, { timeout: 20000 })
      } else {
        throw new Error(`Magic link verification failed: ${errorText}`)
      }
    } else {
      // No error message found, try creating a new token anyway
      console.log('No error message found, retrying with new token...')
      const newToken = await createUniqueToken(email)
      await page.waitForTimeout(1000)
      await page.goto(`/auth/magic-link#${newToken}`)
      await page.waitForURL(/\/(dashboard|$)/, { timeout: 20000 })
    }
  }
}

/**
 * Sign in an admin user using the proper API flow
 */
export async function signInAdmin(page: Page, email: string = '<EMAIL>', token?: string) {
  // Create a unique token if none provided
  if (!token) {
    token = await createUniqueToken(email)
  }

  // Check if rate limiting is disabled for testing
  const rateLimitingDisabled = process.env.DISABLE_RATE_LIMITING === 'true'

  // Add delay only if rate limiting is enabled
  if (!rateLimitingDisabled) {
    await page.waitForTimeout(10000)
  }

  // Navigate to sign-in page
  await page.goto('/sign-in')

  // Fill in email and submit
  await page.fill('input[type="email"]', email)
  await page.click('button[type="submit"]')

  // Wait for either success or error message with retry logic
  let retryCount = 0
  const maxRetries = rateLimitingDisabled ? 1 : 3 // Fewer retries when rate limiting is disabled

  while (retryCount < maxRetries) {
    try {
      await page.waitForSelector('text=Magic link sent! Please check your email', { timeout: 10000 })
      break // Success, exit retry loop
    } catch (error) {
      // Check if there's an error message instead
      const errorElement = await page.locator('.bg-red-50').first()
      if (await errorElement.isVisible()) {
        const errorText = await errorElement.textContent()
        if ((errorText?.includes('Too many requests') || errorText?.includes('rate limit')) && !rateLimitingDisabled) {
          retryCount++
          if (retryCount < maxRetries) {
            const waitTime = 30000 + (retryCount * 10000) // Increasing wait time
            console.log(`Rate limit detected for admin (attempt ${retryCount}), waiting ${waitTime/1000} seconds...`)
            await page.waitForTimeout(waitTime)
            await page.click('button[type="submit"]')
          } else {
            throw new Error(`Rate limit exceeded after ${maxRetries} attempts`)
          }
        } else {
          throw new Error(`Sign-in failed with error: ${errorText}`)
        }
      } else {
        if (retryCount === maxRetries - 1) {
          throw error
        }
        retryCount++
        console.log(`Retrying admin sign-in (attempt ${retryCount})...`)
        await page.waitForTimeout(rateLimitingDisabled ? 1000 : 5000) // Shorter wait when rate limiting disabled
        await page.click('button[type="submit"]')
      }
    }
  }

  // Now simulate clicking the magic link by navigating to the magic link page
  await page.goto(`/auth/magic-link#${token}`)

  // Wait for verification to complete and redirect to dashboard
  try {
    await page.waitForURL(/\/(admin|dashboard)/, { timeout: 20000 })
  } catch (error) {
    // If magic link fails, check for error messages and retry with new token
    console.log('Admin magic link verification failed, checking for errors...')
    await page.waitForTimeout(2000) // Wait for any error messages to appear

    const errorElement = await page.locator('.bg-red-50, .text-red-600, .error').first()
    if (await errorElement.isVisible()) {
      const errorText = await errorElement.textContent()
      console.log('Admin error text found:', errorText)

      if (errorText?.includes('Invalid or expired') || errorText?.includes('Magic link')) {
        console.log('Admin magic link expired, creating new token and retrying...')
        const newToken = await createUniqueToken(email)
        await page.waitForTimeout(1000) // Wait for token creation
        await page.goto(`/auth/magic-link#${newToken}`)
        await page.waitForURL(/\/(admin|dashboard)/, { timeout: 20000 })
      } else {
        throw new Error(`Admin magic link verification failed: ${errorText}`)
      }
    } else {
      // No error message found, try creating a new token anyway
      console.log('No admin error message found, retrying with new token...')
      const newToken = await createUniqueToken(email)
      await page.waitForTimeout(1000)
      await page.goto(`/auth/magic-link#${newToken}`)
      await page.waitForURL(/\/(admin|dashboard)/, { timeout: 20000 })
    }
  }
}

/**
 * Sign in a super admin user using the proper API flow
 */
export async function signInSuperAdmin(page: Page, email: string = '<EMAIL>', token?: string) {
  // Create a unique token if none provided
  if (!token) {
    token = await createUniqueToken(email)
  }

  // Navigate to sign-in page
  await page.goto('/sign-in')

  // Fill in email and submit
  await page.fill('input[type="email"]', email)
  await page.click('button[type="submit"]')

  // Wait for the magic link sent message
  await page.waitForSelector('text=Magic link sent! Please check your email', { timeout: 10000 })

  // Now simulate clicking the magic link by navigating to the magic link page
  await page.goto(`/auth/magic-link#${token}`)

  // Wait for verification to complete and redirect to dashboard
  await page.waitForURL(/\/(admin|dashboard)/, { timeout: 15000 })
}

/**
 * Authenticate using a magic link token via the API
 */
async function authenticateWithToken(page: Page, token: string) {
  // Call the magic link verification API directly
  const response = await page.evaluate(async (authToken) => {
    const res = await fetch('/api/auth/magic-link', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ token: authToken }),
    })
    
    return {
      ok: res.ok,
      status: res.status,
      data: await res.json()
    }
  }, token)
  
  if (!response.ok) {
    throw new Error(`Authentication failed: ${response.status} - ${JSON.stringify(response.data)}`)
  }
  
  // Wait a moment for the session to be established
  await page.waitForTimeout(1000)
}

/**
 * Sign up a new user using the proper API flow
 */
export async function signUpUser(page: Page, email: string, firstName: string, lastName: string) {
  // Navigate to sign-up page
  await page.goto('/sign-up')

  // Fill in the form
  await page.fill('input[name="email"]', email)
  await page.fill('input[name="firstName"]', firstName)
  await page.fill('input[name="lastName"]', lastName)
  await page.click('button[type="submit"]')

  // Wait for the magic link sent message
  await page.waitForSelector('text=Magic link sent', { timeout: 10000 })

  // For testing, we'll create a sign-up token and authenticate directly
  // In a real scenario, this would be handled by clicking the email link
  const signUpToken = `mock-signup-${email.replace('@', '-').replace('.', '-')}`

  // Create the sign-up token in the database via direct API call
  await page.evaluate(async (tokenData) => {
    // Create a magic link token for sign-up with user data
    const response = await fetch('/api/test/create-signup-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(tokenData),
    })

    if (!response.ok) {
      console.warn('Failed to create sign-up token, using fallback')
    }
  }, { token: signUpToken, email, firstName, lastName })

  // Authenticate with the sign-up token
  await authenticateWithToken(page, signUpToken)

  // Wait for redirect to dashboard
  await page.waitForURL(/\/dashboard/, { timeout: 10000 })
}

/**
 * Wait for page to fully load
 */
export async function waitForPageLoad(page: Page) {
  await page.waitForLoadState('domcontentloaded')
  await page.waitForTimeout(2000) // Additional wait for dynamic content
}

/**
 * Clear all authentication state
 */
export async function clearAuth(page: Page) {
  await page.context().clearCookies()

  // Navigate to a page first to ensure we have access to localStorage
  try {
    await page.goto('/')
    await page.evaluate(() => {
      localStorage.clear()
      sessionStorage.clear()
    })
  } catch (error) {
    // If localStorage access fails, just clear cookies (which is the main auth mechanism)
    console.warn('Could not clear localStorage/sessionStorage:', error)
  }
}
