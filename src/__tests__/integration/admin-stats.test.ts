/**
 * Admin Stats API Integration Tests
 * Tests the /api/admin/stats endpoint
 */

import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import {
  setupTestDatabase,
  cleanupTestDatabase,
  createTestUserSession,
  createAuthHeaders
} from './setup'

const BASE_URL = process.env.TEST_SERVER_URL || 'http://localhost:3000'

describe('Admin Stats API', () => {
  beforeAll(async () => {
    await setupTestDatabase()
  })

  afterAll(async () => {
    await cleanupTestDatabase()
  })

  it('should return admin statistics for authenticated admin', async () => {
    // Create admin session and get proper auth headers
    const sessionToken = await createTestUserSession('<EMAIL>')
    const headers = createAuthHeaders(sessionToken)

    const response = await fetch(`${BASE_URL}/api/admin/stats`, { headers })

    expect(response.status).toBe(200)
    
    const data = await response.json()
    
    // Check that the response has the expected structure
    expect(data).toHaveProperty('totalCompanies')
    expect(data).toHaveProperty('totalUsers')
    expect(data).toHaveProperty('totalBenefits')
    
    // Check that the values are numbers
    expect(typeof data.totalCompanies).toBe('number')
    expect(typeof data.totalUsers).toBe('number')
    expect(typeof data.totalBenefits).toBe('number')
    
    // Check that the values are non-negative
    expect(data.totalCompanies).toBeGreaterThanOrEqual(0)
    expect(data.totalUsers).toBeGreaterThanOrEqual(0)
    expect(data.totalBenefits).toBeGreaterThanOrEqual(0)
    
    console.log('Admin stats:', data)
  })

  it('should require admin authentication', async () => {
    // Test without authentication
    const response = await fetch(`${BASE_URL}/api/admin/stats`)

    expect(response.status).toBe(401)
  })

  it('should reject non-admin users', async () => {
    // Test with regular user token (non-admin)
    const sessionToken = await createTestUserSession('<EMAIL>')
    const headers = createAuthHeaders(sessionToken)

    const response = await fetch(`${BASE_URL}/api/admin/stats`, { headers })

    expect(response.status).toBe(403)
  })
})
