#!/usr/bin/env node

/**
 * Simple test runner to verify test fixes
 * This bypasses the terminal quote issue by running tests directly
 */

const { spawn } = require('child_process');
const path = require('path');

function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    console.log(`\n🔧 Running: ${command} ${args.join(' ')}`);
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      cwd: process.cwd(),
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ ${command} completed successfully`);
        resolve(code);
      } else {
        console.log(`❌ ${command} failed with code ${code}`);
        reject(new Error(`Command failed with code ${code}`));
      }
    });

    child.on('error', (error) => {
      console.error(`❌ Error running ${command}:`, error.message);
      reject(error);
    });
  });
}

async function runTests() {
  console.log('🧪 BenefitLens Test Runner');
  console.log('==========================');
  
  try {
    // Test 1: Run unit tests
    console.log('\n📋 Step 1: Running Unit Tests');
    await runCommand('npx', ['vitest', '--config', 'vitest.config.ts', '--run']);
    
    // Test 2: Check integration test config
    console.log('\n📋 Step 2: Checking Integration Test Config');
    await runCommand('npx', ['vitest', '--config', 'vitest.integration.config.ts', '--run', '--reporter=verbose']);
    
    // Test 3: Run E2E essential tests
    console.log('\n📋 Step 3: Running Essential E2E Tests');
    await runCommand('npx', ['playwright', 'test', '--config=playwright-conservative.config.ts', 'src/__tests__/e2e/essential-tests.spec.ts']);
    
    console.log('\n🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('\n💥 Test run failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests, runCommand };
